<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google OAuth Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .btn { padding: 12px 24px; margin: 10px; border: none; border-radius: 5px; cursor: pointer; }
        .google-btn { background: #4285f4; color: white; }
        .logout-btn { background: #dc3545; color: white; }
        .user-info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Google OAuth Test</h1>
    
    <div id="loginSection">
        <button class="btn google-btn" onclick="loginWithGoogle()">
            Sign in with Google
        </button>
    </div>

    <div id="userSection" style="display: none;">
        <div class="user-info">
            <h3>Welcome!</h3>
            <p>You are successfully logged in via Google OAuth.</p>
            <button class="btn logout-btn" onclick="logout()">Logout</button>
        </div>
    </div>

    <div id="errorSection" style="display: none; color: red;">
        <p id="errorMessage"></p>
    </div>

    <script>
        function loginWithGoogle() {
            window.location.href = 'http://localhost:3000/auth/google';
        }

        function logout() {
            // Clear cookies by setting them to expire
            document.cookie = 'accessToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            document.cookie = 'refreshToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            
            document.getElementById('loginSection').style.display = 'block';
            document.getElementById('userSection').style.display = 'none';
        }

        // Check URL parameters for auth results
        window.onload = function() {
            const urlParams = new URLSearchParams(window.location.search);
            const path = window.location.pathname;
            
            if (path.includes('/auth/success')) {
                document.getElementById('loginSection').style.display = 'none';
                document.getElementById('userSection').style.display = 'block';
            } else if (path.includes('/auth/error')) {
                const errorMessage = urlParams.get('message') || 'Authentication failed';
                document.getElementById('errorMessage').textContent = errorMessage;
                document.getElementById('errorSection').style.display = 'block';
            }
        }
    </script>
</body>
</html>
