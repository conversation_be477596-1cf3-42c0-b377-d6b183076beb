import { Injectable, BadRequestException } from "@nestjs/common";
import { UserService } from "../user/user.service";

@Injectable()
export class OauthService {
  constructor(private readonly userService: UserService) { }

  async handleGoogleLogin(googleProfile: any) {
    try {
      let user = await this.userService.findByEmail(googleProfile.email);
      if (!user) {
        user = await this.userService.createUserFromGoogle(googleProfile);
      }
      return this.userService.generateUserToken(user.id);
    } catch (error) {
      throw new BadRequestException('OAuth login failed');
    }
  }
}
