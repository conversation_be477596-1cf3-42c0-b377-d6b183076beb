import { Injectable } from "@nestjs/common";
import * as nodemailer from 'nodemailer';

@Injectable()
export class MailService {
  private readonly transporter : nodemailer.Transporter;

  constructor(){
    this.transporter = nodemailer.createTransport({
        host: "sandbox.smtp.mailtrap.io",
        port: 2525, // You can use 25, 465, 587, or 2525
        secure: false, // false for ports 25, 587, 2525. true for 465
        auth: {
          user: "942e358b04efc9", // Your Mailtrap username
          pass: "469f7f5d2f557e", // Your Mailtrap password - use full password
        },
    });
  }

  async sendPasswordResetEmail(to: string, token: string) {
    const resetLink = `http://localhost:3000/user/reset-password?token=${token}`;
    const mailOptions = {
      from: "Auth Backend Services",
      to: to,
      subject: "🔐 Password Reset Request",
      text: `You requested a password reset. Use this link to reset your password: ${resetLink} (This link expires in 1 hour)`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
          <h2 style="color: #007bff; text-align: center; margin-bottom: 30px;">🔐 Password Reset</h2>
          
          <p style="font-size: 16px; line-height: 1.5;">Hello,</p>
          <p style="font-size: 16px; line-height: 1.5;">You requested a password reset for your account.</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetLink}" 
               style="background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block; font-size: 16px;">
              Reset Password
            </a>
          </div>
          
          <div style="background: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin: 20px 0; border-radius: 4px;">
            <p style="margin: 0; color: #856404;"><strong>⚠️ Important:</strong></p>
            <ul style="margin: 10px 0 0 0; color: #856404;">
              <li>This link will expire in <strong>1 hour</strong></li>
              <li>If you didn't request this reset, please ignore this email</li>
              <li>For security, never share this link with anyone</li>
            </ul>
          </div>
          
          <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">
          <p style="color: #666; font-size: 14px; text-align: center;">
            Having trouble with the button? <a href="${resetLink}" style="color: #007bff;">Click here</a>
          </p>
          <p style="color: #999; font-size: 12px; text-align: center;">
            © 2025 Your App Name. All rights reserved.
          </p>
        </div>
      `,
    };
    try{
       const info = await this.transporter.sendMail(mailOptions);
       console.log('✅ Email sent successfully:', info.messageId);
       return { success: true, messageId: info.messageId };
    }catch(error){
      console.error('❌ Error sending email:', error);
      return { success: false, error: error.message };
    }

  }
}