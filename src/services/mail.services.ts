import { Injectable } from "@nestjs/common";
import * as nodemailer from 'nodemailer';

@Injectable()
export class MailService {
  private readonly transporter : nodemailer.Transporter;

  constructor(){
    this.transporter = nodemailer.createTransport({
        host: "sandbox.smtp.mailtrap.io",
        port: 2525, // You can use 25, 465, 587, or 2525
        secure: false, // false for ports 25, 587, 2525. true for 465
        auth: {
          user: "942e358b04efc9", // Mailtrap username
          pass: "469f7f5d2f557e", // Mailtrap password - use full password
        },
    });
  }

  async sendOtpEmail(to: string, otpCode: string) {
    const mailOptions = {
      from: '"AvenueSpace" <<EMAIL>>',
      to: to,
      subject: 'Verify Your Email - AvenueSpace',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #333; font-size: 24px;">AvenueSpace</h1>
          </div>
          
          <div style="background-color: #f8f9fa; padding: 30px; border-radius: 8px; text-align: center;">
            <h2 style="color: #333; margin-bottom: 20px;">Your verification code is:</h2>
            
            <div style="font-size: 32px; font-weight: bold; color: #007bff; letter-spacing: 8px; margin: 20px 0; padding: 20px; background-color: white; border-radius: 8px; border: 2px dashed #007bff;">
              ${otpCode}
            </div>
            
            <p style="color: #666; margin-top: 20px;">This code expires in 10 minutes.</p>
            <p style="color: #666; font-size: 14px;">Don't share this code with anyone.</p>
          </div>
          
          <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
            <p style="color: #999; font-size: 12px;">© 2025 AvenueSpace. All rights reserved.</p>
          </div>
        </div>
      `,
    };

    try {
      const info = await this.transporter.sendMail(mailOptions);
      console.log('🤩 OTP email sent successfully:', info.messageId);
      return { success: true, messageId: info.messageId };
    } catch (error) {
      console.error('😢 Error sending OTP email:', error);
      throw new Error('Failed to send OTP email');
    }
  }

  async sendPasswordResetOtpEmail(to: string, otpCode: string) {
    const mailOptions = {
      from: '"AvenueSpace" <<EMAIL>>',
      to: to,
      subject: 'Reset Your Password - AvenueSpace',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #333; font-size: 24px;">AvenueSpace</h1>
          </div>
          
          <div style="background-color: #f8f9fa; padding: 30px; border-radius: 8px; text-align: center;">
            <h2 style="color: #333; margin-bottom: 20px;">Reset Your Password</h2>
            <p style="color: #666; margin-bottom: 20px;">Your password reset code is:</p>
            
            <div style="font-size: 32px; font-weight: bold; color: #dc3545; letter-spacing: 8px; margin: 20px 0; padding: 20px; background-color: white; border-radius: 8px; border: 2px dashed #dc3545;">
              ${otpCode}
            </div>
            
            <p style="color: #666; margin-top: 20px;">This code expires in 10 minutes.</p>
            <p style="color: #666; font-size: 14px;">If you didn't request this, please ignore this email.</p>
          </div>
          
          <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
            <p style="color: #999; font-size: 12px;">© 2025 AvenueSpace. All rights reserved.</p>
          </div>
        </div>
      `,
    };

    try {
      const info = await this.transporter.sendMail(mailOptions);
      console.log('✅ Password reset OTP email sent successfully:', info.messageId);
      return { success: true, messageId: info.messageId };
    } catch (error) {
      console.error('❌ Error sending password reset OTP email:', error);
      throw new Error('Failed to send password reset OTP email');
    }
  }

  async sendPasswordResetEmail(to: string, token: string) {
    const resetLink = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/reset-password?token=${token}`;
    const mailOptions = {
      from: '"AvenueSpace" <<EMAIL>>',
      to: to,
      subject: 'Reset Your Password - AvenueSpace',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #333; font-size: 24px;">AvenueSpace</h1>
          </div>
          
          <div style="background-color: #f8f9fa; padding: 30px; border-radius: 8px;">
            <h2 style="color: #333; margin-bottom: 20px;">Reset Your Password</h2>
            
            <p style="color: #666; margin-bottom: 20px;">
              You requested to reset your password. Click the button below to reset it:
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${resetLink}" style="background-color: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
                Reset Password
              </a>
            </div>
            
            <p style="color: #666; font-size: 14px;">
              This link will expire in 1 hour. If you didn't request this, please ignore this email.
            </p>
            
            <p style="color: #999; font-size: 12px; margin-top: 20px;">
              If the button doesn't work, copy and paste this link into your browser:<br>
              <a href="${resetLink}" style="color: #dc3545;">${resetLink}</a>
            </p>
          </div>
          
          <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
            <p style="color: #999; font-size: 12px;">© 2025 AvenueSpace. All rights reserved.</p>
          </div>
        </div>
      `,
    };

    try {
      const info = await this.transporter.sendMail(mailOptions);
      console.log('✅ Password reset email sent successfully:', info.messageId);
      return { success: true, messageId: info.messageId };
    } catch (error) {
      console.error('❌ Error sending password reset email:', error);
      throw new Error('Failed to send password reset email');
    }
  }
}