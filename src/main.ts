import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import * as express from 'express';
import * as cookieParser from 'cookie-parser';
import configuration from 'config/configuration';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ValidationPipe } from '@nestjs/common';


async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  app.use(cookieParser());
  
  // Enable CORS
  app.enableCors({
    origin: ['http://localhost:5173', 'http://localhost:3000', 'http://localhost:8080'],
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    credentials: true,
  });

  app.useGlobalPipes(
    new ValidationPipe({ 
      whitelist: true, 
      forbidNonWhitelisted: true, 
    }),
  )

  app.use(express.json({ limit: '10mb' }));  // need if expecting large payloads
  app.use(express.urlencoded({ limit: '10mb', extended: true }));

  const config = new DocumentBuilder()
    .setTitle('Authentication API')
    .setDescription('Auth Boiler plate API documentation')
    .setVersion('1.0')
    .build();
  
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  if (configuration().database.seeder_sync === 'true') {
    console.log('🌱 Running seeders...');
    // Run your seeders here. Example:
    // await SomeEntitySeeder.seed();
    // await AnotherEntitySeeder.seed();
    // Add more seeders as needed
    console.log('✅ All seeders completed');
  }


  console.log(`🚀 Server running on http://localhost:${configuration().port}`);
  await app.listen(configuration().port, '0.0.0.0');
}
bootstrap();
