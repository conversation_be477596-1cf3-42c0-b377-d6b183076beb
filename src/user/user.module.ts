import { Module, forwardRef } from '@nestjs/common';
import { UserController } from './user.controller';
import { UserService } from './user.service';
import { OauthService } from '../services/oauth.service';
import { AuthModule } from 'src/auth/auth.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { Auth_tokens } from './entities/auth_tokens.entity';
import { MailService } from 'src/services/mail.services';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Auth_tokens]),
    forwardRef(() => AuthModule)
  ],
  controllers: [UserController],
  providers: [UserService, OauthService, MailService],
  exports: [UserService, OauthService],
})
export class UserModule { }
