import { <PERSON><PERSON>ty, <PERSON>umn, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn,} from 'typeorm';
import { User } from './user.entity';

export enum TokenType {
  REFRESH = 'refresh',
  RESET = 'reset',
}

@Entity('auth_tokens')
export class Auth_tokens {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  token : string;

  @Column()
  user_id: number;

  @Column()
  expires_at: Date;

  @Column()
  type: TokenType ;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}

