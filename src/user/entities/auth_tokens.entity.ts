import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, Unique } from 'typeorm';

export enum TokenType {
  REFRESH = 'refresh',
  RESET = 'reset',
}

@Entity('auth_tokens')
@Unique(['user_id', 'type'])
export class Auth_tokens {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  token: string;

  @Column()
  user_id: number;

  @Column()
  expires_at: Date;

  @Column()
  type: TokenType;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}

