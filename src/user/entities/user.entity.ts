import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedC<PERSON>umn, CreateDateColumn, UpdateDateColumn,} from 'typeorm';
import { Exclude } from 'class-transformer';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  name: string;

  @Column({ unique: true })
  email: string;

  @Column({ nullable: true }) 
  @Exclude()
  password: string;

  @Column({ nullable: true })
  google_id: string;

  @Column({ default: false })
  is_verified: boolean;

  @Column({ nullable: true })
  @Exclude()
  otp_code: string;

  @Column({ nullable: true })
  @Exclude()
  otp_expires_at: Date;

  @Column({ default: 0 })
  @Exclude()
  otp_attempts: number;

  @Column({ nullable: true })
  @Exclude()
  last_otp_request: Date;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}