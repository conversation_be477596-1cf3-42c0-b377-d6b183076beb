import { BadRequestException, Injectable, UnauthorizedException } from '@nestjs/common';
import { EntityManager } from 'typeorm';
import { User } from './entities/user.entity';
import * as bcrypt from 'bcrypt';
import { signupDto } from './dto/signup.dto';
import { loginDto } from './dto/login.dto';
import { JwtService } from '@nestjs/jwt';
import { v4 as uuidv4 } from 'uuid';
import { Auth_tokens, TokenType } from './entities/auth_tokens.entity';
import { refreshTokenDto } from './dto/refresh-token.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { forgotPasswordDto } from './dto/forgot-password.dto';
import { nanoid } from 'nanoid';
import { MailService } from 'src/services/mail.services';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ResetPasswordOtpDto } from './dto/reset-password-otp.dto';

@Injectable()
export class UserService {
  constructor(
    private entityManager: EntityManager,
    private jwtService: JwtService,
    private mailService: MailService,
  ) { }

  private async normalizeEmail(email: string): Promise<string> {
    return email.toLowerCase().trim();
  }

  async findByEmail(email: string): Promise<User | null> {
    return await this.entityManager
      .createQueryBuilder(User, 'user')
      .where('user.email = :email', { email })
      .getOne();
  }

  async findById(id: number): Promise<User | null> {
    return await this.entityManager
      .createQueryBuilder(User, 'user')
      .where('user.id = :id', { id })
      .select(['user.id', 'user.name', 'user.email', 'user.created_at', 'user.updated_at'])
      .getOne();
  }


  async signup(signupDto: signupDto) {
    // Check if user with email already exists
    const email = await this.normalizeEmail(signupDto.email);
    const existingUser = await this.findByEmail(email);

    if (existingUser && existingUser.is_verified) {
      throw new BadRequestException('Email already in use');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(signupDto.password, 12);

    // Generate OTP
    const otpCode = Math.floor(100000 + Math.random() * 900000).toString();
    const otpExpiresAt = new Date();
    otpExpiresAt.setMinutes(otpExpiresAt.getMinutes() + 10); // 10 minutes

    if (existingUser && !existingUser.is_verified) {
      // Update existing unverified user
      await this.entityManager
        .createQueryBuilder()
        .update(User)
        .set({
          name: signupDto.name,
          password: hashedPassword,
          otp_code: otpCode,
          otp_expires_at: otpExpiresAt,
          otp_attempts: 0,
          last_otp_request: new Date()
        })
        .where('email = :email', { email })
        .execute();
    } else {
      // Create new user
      await this.entityManager
        .createQueryBuilder(User, 'user')
        .insert()
        .values({
          name: signupDto.name,
          email: email,
          password: hashedPassword,
          is_verified: false,
          otp_code: otpCode,
          otp_expires_at: otpExpiresAt,
          otp_attempts: 0,
          last_otp_request: new Date()
        })
        .execute();
    }

    // Send OTP email
    await this.mailService.sendOtpEmail(email, otpCode);

    return {
      message: 'Signup successful. Please check your email for verification code.',
      email: email,
      isReSignup: !!existingUser
    };
  }

  async createUserFromGoogle(googleProfile: { id?: string; email: string; firstName?: string; lastName?: string; picture?: string; }): Promise<User> {
    const email = await this.normalizeEmail(googleProfile.email);
    const existing = await this.findByEmail(email);
    if (existing) {
      // Auto-verify Google users if not already verified
      if (!existing.is_verified) {
        await this.entityManager
          .createQueryBuilder()
          .update(User)
          .set({ is_verified: true })
          .where('id = :id', { id: existing.id })
          .execute();
        existing.is_verified = true;
      }
      return existing;
    }

    const nameParts: string[] = [];
    if (googleProfile.firstName) nameParts.push(googleProfile.firstName);
    if (googleProfile.lastName) nameParts.push(googleProfile.lastName);
    const fullName = nameParts.join(' ').trim() || null;

    const insertResult = await this.entityManager
      .createQueryBuilder(User, 'user')
      .insert()
      .values({
        name: fullName,
        email: email,
        password: null,
        google_id: googleProfile.id || null,
        is_verified: true, // Auto-verify Google users
      })
      .returning('*')
      .execute();

    return insertResult.raw[0];
  }

  async login(loginDto: loginDto) {
    // find if user exist by email 
    const email = await this.normalizeEmail(loginDto.email);
    const user = await this.findByEmail(email);

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Check if user has a password (OAuth users don't)
    if (!user.password) {
      throw new BadRequestException('This account uses Google sign-in. Please use "Sign in with Google" instead.');
    }

    // compare password
    const passwordMatches = await bcrypt.compare(loginDto.password, user.password);
    if (!passwordMatches) {
      throw new BadRequestException('Invalid credentials');
    }

    // Check if user is verified
    if (!user.is_verified) {
      // Generate new OTP for unverified user
      const otpCode = Math.floor(100000 + Math.random() * 900000).toString();
      const otpExpiresAt = new Date();
      otpExpiresAt.setMinutes(otpExpiresAt.getMinutes() + 10);

      await this.entityManager
        .createQueryBuilder()
        .update(User)
        .set({
          otp_code: otpCode,
          otp_expires_at: otpExpiresAt,
          otp_attempts: 0,
          last_otp_request: new Date()
        })
        .where('email = :email', { email })
        .execute();

      // Send OTP email
      await this.mailService.sendOtpEmail(email, otpCode);

      // Return special error with requiresVerification flag
      throw new UnauthorizedException({
        message: 'Please verify your email first',
        requiresVerification: true,
        email: email
      });
    }

    delete user.password;

    // generate JWT token
    const tokens = await this.generateUserToken(user.id);

    return {
      message: 'Login successful',
      tokens
    }
  }

  async refreshToken(refreshTokenDto: refreshTokenDto) {
    // Single atomic operation: find valid token and invalidate it
    const result = await this.entityManager
      .createQueryBuilder()
      .update(Auth_tokens)
      .set({ expires_at: new Date() })
      .where('token = :token', { token: refreshTokenDto.refreshToken })
      .andWhere('type = :type', { type: TokenType.REFRESH })
      .andWhere('expires_at > :now', { now: new Date() })
      .returning('user_id')
      .execute();

    if (!result.affected || result.affected === 0) {
      throw new UnauthorizedException('Invalid or expired refresh token');
    }

    const userId = result.raw[0]?.user_id;
    if (!userId) {
      throw new UnauthorizedException('Invalid token');
    }

    return this.generateUserToken(userId);
  }

  async generateUserToken(userId: number): Promise<any> {
    const accessToken = this.jwtService.sign({ id: userId }, { expiresIn: '24h' });
    const refreshToken = uuidv4();

    await this.storeRefreshToken(userId, refreshToken);

    return {
      accessToken,
      refreshToken
    };
  }

  async storeRefreshToken(userId: number, token: string) {
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 3); // 3 days

    // Check if refresh token already exists for this user
    const existingToken = await this.entityManager
      .createQueryBuilder(Auth_tokens, 'auth_token')
      .where('auth_token.user_id = :userId', { userId })
      .andWhere('auth_token.type = :type', { type: TokenType.REFRESH })
      .getOne();

    if (existingToken) {
      // Update existing token
      await this.entityManager
        .createQueryBuilder()
        .update(Auth_tokens)
        .set({ token: token, expires_at: expiresAt })
        .where('id = :id', { id: existingToken.id })
        .execute();
    } else {
      // Insert new token
      await this.entityManager
        .createQueryBuilder()
        .insert()
        .into(Auth_tokens)
        .values({
          token: token,
          user_id: userId,
          expires_at: expiresAt,
          type: TokenType.REFRESH,
        })
        .execute();
    }
  }

  async changePassword(changePasswordDto: ChangePasswordDto, userId: number) {
    // find user by id and get password
    const user = await this.entityManager
      .createQueryBuilder(User, 'user')
      .where('user.id = :id', { id: userId })
      .getOne();

    if (!user) {
      throw new UnauthorizedException('User not found...... ');
    }

    // Check if user has a password (OAuth users don't)
    if (!user.password) {
      throw new BadRequestException('OAuth users cannot change password. Your account uses Google sign-in.');
    }

    // compare old password with one in db 
    const passwordMatches = await bcrypt.compare(changePasswordDto.oldPassword, user.password);
    if (!passwordMatches) {
      throw new BadRequestException('Old password is incorrect');
    }

    // change user password ( Dont forget to hash)
    const hashedNewPassword = await bcrypt.hash(changePasswordDto.newPassword, 12); // 12 salt rounds
    await this.entityManager.createQueryBuilder()
      .update(User)
      .set({ password: hashedNewPassword })
      .where('id = :id', { id: userId })
      .execute();

    return { message: 'Password changed successfully' };
  }

  async forgotPassword(forgotPasswordDto: forgotPasswordDto) {
    // check if user exist 
    const email = await this.normalizeEmail(forgotPasswordDto.email);
    const user = await this.findByEmail(email);

    if (!user) {
      throw new BadRequestException('No account found with this email address');
    }

    if (!user.is_verified) {
      throw new BadRequestException('Please verify your email first before resetting password');
    }

    // Generate reset token for email link
    const resetToken = nanoid(64);
    const expiryDate = new Date();
    expiryDate.setHours(expiryDate.getHours() + 1); // 1 hour expiry

    // Check if reset token already exists for this user
    const existingToken = await this.entityManager
      .createQueryBuilder(Auth_tokens, 'auth_token')
      .where('auth_token.user_id = :userId', { userId: user.id })
      .andWhere('auth_token.type = :type', { type: TokenType.RESET })
      .getOne();

    if (existingToken) {
      // Update existing token
      await this.entityManager
        .createQueryBuilder()
        .update(Auth_tokens)
        .set({ token: resetToken, expires_at: expiryDate })
        .where('id = :id', { id: existingToken.id })
        .execute();
    } else {
      // Insert new token
      await this.entityManager
        .createQueryBuilder()
        .insert()
        .into(Auth_tokens)
        .values({
          token: resetToken,
          user_id: user.id,
          expires_at: expiryDate,
          type: TokenType.RESET,
        })
        .execute();
    }

    // Send reset link email
    await this.mailService.sendPasswordResetEmail(email, resetToken);

    return { message: 'Password reset link sent to your email' };
  }

  // Keep OTP method as backup for future use
  async forgotPasswordOtp(forgotPasswordDto: forgotPasswordDto) {
    const email = await this.normalizeEmail(forgotPasswordDto.email);
    const user = await this.findByEmail(email);

    if (!user) {
      throw new BadRequestException('No account found with this email address');
    }

    if (!user.is_verified) {
      throw new BadRequestException('Please verify your email first before resetting password');
    }

    // Generate OTP for password reset
    const otpCode = Math.floor(100000 + Math.random() * 900000).toString();
    const otpExpiresAt = new Date();
    otpExpiresAt.setMinutes(otpExpiresAt.getMinutes() + 10);

    await this.entityManager
      .createQueryBuilder()
      .update(User)
      .set({
        otp_code: otpCode,
        otp_expires_at: otpExpiresAt,
        otp_attempts: 0,
        last_otp_request: new Date()
      })
      .where('email = :email', { email })
      .execute();

    await this.mailService.sendPasswordResetOtpEmail(email, otpCode);

    return { message: 'Password reset code sent to your email' };
  }

  async resetPasswordWithOtp(resetPasswordOtpDto: ResetPasswordOtpDto) {
    // Validate passwords match
    if (resetPasswordOtpDto.newPassword !== resetPasswordOtpDto.confirmPassword) {
      throw new BadRequestException('Passwords do not match');
    }

    const email = await this.normalizeEmail(resetPasswordOtpDto.email);
    const user = await this.findByEmail(email);

    if (!user) {
      throw new BadRequestException('No account found with this email address');
    }

    if (!user.is_verified) {
      throw new BadRequestException('Please verify your email first');
    }

    if (!user.otp_code || user.otp_code !== resetPasswordOtpDto.otp) {
      throw new UnauthorizedException('Invalid OTP code');
    }

    if (!user.otp_expires_at || user.otp_expires_at < new Date()) {
      throw new UnauthorizedException('OTP code has expired. Please request a new one.');
    }

    // Update password and clear OTP data
    const hashedNewPassword = await bcrypt.hash(resetPasswordOtpDto.newPassword, 12);
    await this.entityManager
      .createQueryBuilder()
      .update(User)
      .set({
        password: hashedNewPassword,
        otp_code: null,
        otp_expires_at: null,
        otp_attempts: 0,
        last_otp_request: null
      })
      .where('email = :email', { email })
      .execute();

    return { message: 'Password has been reset successfully' };
  }

  async verifyResetToken(token: string) {
    const storedToken = await this.entityManager
      .createQueryBuilder(Auth_tokens, 'reset_token')
      .where('reset_token.token = :token', { token })
      .andWhere('reset_token.type = :type', { type: TokenType.RESET })
      .andWhere('reset_token.expires_at > :now', { now: new Date() })
      .getOne();

    if (!storedToken) {
      throw new UnauthorizedException('Invalid or expired reset token');
    }

    return storedToken;
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto) {
    // find token in db
    const storedToken = await this.entityManager
      .createQueryBuilder(Auth_tokens, 'reset_token')
      .where('reset_token.token = :token', { token: resetPasswordDto.resetToken })
      .andWhere('reset_token.type = :type', { type: TokenType.RESET })
      .andWhere('reset_token.expires_at > :now', { now: new Date() }) // Ensure token is not expired
      .getOne();

    if (!storedToken) {
      throw new UnauthorizedException('Invalid or expired reset token');
    }

    // find user by id
    const user = await this.entityManager
      .createQueryBuilder(User, 'user')
      .where('user.id = :id', { id: storedToken.user_id })
      .getOne();

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // update user password
    const hashedNewPassword = await bcrypt.hash(resetPasswordDto.newPassword, 12); // 12 salt rounds
    await this.entityManager.createQueryBuilder()
      .update(User)
      .set({ password: hashedNewPassword })
      .where('id = :id', { id: user.id })
      .execute();

    // invalidate the used reset token
    await this.entityManager.createQueryBuilder()
      .update(Auth_tokens)
      .set({ expires_at: new Date() }) // Invalidate the used reset token
      .where("id = :id", { id: storedToken.id })
      .execute();

    return { message: 'Password has been reset successfully' };
  }

  async verifyOtp(verifyOtpDto: { email: string; otp: string }) {
    const email = await this.normalizeEmail(verifyOtpDto.email);
    const user = await this.findByEmail(email);

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    if (user.is_verified) {
      throw new BadRequestException('Email already verified');
    }

    if (!user.otp_code || user.otp_code !== verifyOtpDto.otp) {
      throw new UnauthorizedException('Invalid OTP');
    }

    if (!user.otp_expires_at || user.otp_expires_at < new Date()) {
      throw new UnauthorizedException('OTP code has expired. Please request a new one.');
    }

    // Verify user and clear OTP data
    await this.entityManager
      .createQueryBuilder()
      .update(User)
      .set({
        is_verified: true,
        otp_code: null,
        otp_expires_at: null,
        otp_attempts: 0,
        last_otp_request: null
      })
      .where('email = :email', { email })
      .execute();

    // Generate tokens for auto-login
    const tokens = await this.generateUserToken(user.id);

    return {
      message: 'Email verified successfully',
      tokens
    };
  }

  async resendOtp(resendOtpDto: { email: string }) {
    const email = await this.normalizeEmail(resendOtpDto.email);
    const user = await this.findByEmail(email);

    if (!user) {
      throw new BadRequestException('No account found with this email address');
    }

    if (user.is_verified) {
      throw new BadRequestException('Email is already verified');
    }

    // Check rate limiting
    const now = new Date();
    const timeSinceLastRequest = user.last_otp_request
      ? (now.getTime() - user.last_otp_request.getTime()) / 1000
      : Infinity;

    // Progressive rate limiting
    if (user.otp_attempts >= 5) {
      const requiredDelay = user.otp_attempts <= 10 ? 60 : 300; // 1 min or 5 min
      if (timeSinceLastRequest < requiredDelay) {
        const waitTime = Math.ceil(requiredDelay - timeSinceLastRequest);
        throw new BadRequestException(`Please wait ${waitTime} seconds before requesting another OTP`);
      }
    }

    // Generate new OTP
    const otpCode = Math.floor(100000 + Math.random() * 900000).toString();
    const otpExpiresAt = new Date();
    otpExpiresAt.setMinutes(otpExpiresAt.getMinutes() + 10);

    // Reset attempts if 10 minutes have passed
    const newAttempts = timeSinceLastRequest > 600 ? 1 : user.otp_attempts + 1;

    await this.entityManager
      .createQueryBuilder()
      .update(User)
      .set({
        otp_code: otpCode,
        otp_expires_at: otpExpiresAt,
        otp_attempts: newAttempts,
        last_otp_request: now
      })
      .where('email = :email', { email })
      .execute();

    // Send OTP email
    await this.mailService.sendOtpEmail(email, otpCode);

    return {
      message: 'OTP sent successfully',
      canResendIn: newAttempts >= 5 ? (newAttempts <= 10 ? 60 : 300) : 0
    };
  }

  // Cleanup expired tokens (run this periodically via cron job)
  async cleanupExpiredTokens() {
    await this.entityManager
      .createQueryBuilder()
      .delete()
      .from(Auth_tokens)
      .where('expires_at < :now', { now: new Date() })
      .execute();
  }

  // Cleanup unverified accounts older than 7 days
  async cleanupUnverifiedAccounts() {
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const result = await this.entityManager
      .createQueryBuilder()
      .delete()
      .from(User)
      .where('is_verified = :isVerified', { isVerified: false })
      .andWhere('created_at < :cutoffDate', { cutoffDate: sevenDaysAgo })
      .execute();

    console.log(`🧹 Cleaned up ${result.affected} unverified accounts older than 7 days`);
    return result.affected;
  }
}
