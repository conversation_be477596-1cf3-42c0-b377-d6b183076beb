import { BadRequestException, Injectable, UnauthorizedException } from '@nestjs/common';
import { Entity<PERSON>anager, MoreThan } from 'typeorm';
import { User } from './entities/user.entity';
import * as bcrypt from 'bcrypt';
import { signupDto } from './dto/signup.dto';
import { loginDto } from './dto/login.dto';
import { JwtService } from '@nestjs/jwt';
import { v4 as uuidv4 } from 'uuid';
import { Auth_tokens, TokenType } from './entities/auth_tokens.entity';
import { refreshTokenDto } from './dto/refresh-token.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { forgotPasswordDto } from './dto/forgot-password.dto';
import { nanoid } from 'nanoid';
import { MailService } from 'src/services/mail.services';
import { ResetPasswordDto } from './dto/reset-password.dto';

@Injectable()
export class UserService {
  constructor(
    private entityManager: EntityManager,
    private jwtService: JwtService,
    private mailService: MailService,
  ) { }

  private async normalizeEmail(email: string): Promise<string> {
    return email.toLowerCase().trim();
  }

  async findByEmail(email: string): Promise<User | null> {
    return await this.entityManager
      .createQueryBuilder(User, 'user')
      .where('user.email = :email', { email })
      .getOne();
  }

  async findById(id: number): Promise<User | null> {
    return await this.entityManager
      .createQueryBuilder(User, 'user')
      .where('user.id = :id', { id })
      .select(['user.id', 'user.name', 'user.email', 'user.created_at', 'user.updated_at'])
      .getOne();
  }


  async signup(signupDto: signupDto) {
    // Check if user with email already exists
    const email = await this.normalizeEmail(signupDto.email);
    const user = await this.findByEmail(email);

    if (user) {
      throw new BadRequestException('Email already in use');
    }
    // Hash password
    const hashedPassword = await bcrypt.hash(signupDto.password, 12); // 12 salt rounds 
    // Save user to database
    const result = await this.entityManager
      .createQueryBuilder(User, 'user')
      .insert()
      .values({
        name: signupDto.name,
        email: email,
        password: hashedPassword,
      })
      .returning('*')
      .execute()

    const newUser = result.raw[0];
    delete newUser.password; // Remove password before returning user data

    // Return user data (excluding password)
    return newUser;
  }

  async createUserFromGoogle(googleProfile: { id?: string; email: string; firstName?: string; lastName?: string; picture?: string; }): Promise<User> {
    const email = await this.normalizeEmail(googleProfile.email);
    const existing = await this.findByEmail(email);
    if (existing) {
      return existing;
    }

    const nameParts: string[] = [];
    if (googleProfile.firstName) nameParts.push(googleProfile.firstName);
    if (googleProfile.lastName) nameParts.push(googleProfile.lastName);
    const fullName = nameParts.join(' ').trim() || null;

    const insertResult = await this.entityManager
      .createQueryBuilder(User, 'user')
      .insert()
      .values({
        name: fullName,
        email: email,
        password: null,
        google_id: googleProfile.id || null,
      })
      .returning('*')
      .execute();

    return insertResult.raw[0];
  }

  async login(loginDto: loginDto) {
    // find if user exist by email 
    const email = await this.normalizeEmail(loginDto.email);
    const user = await this.findByEmail(email);

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // compare password
    const passwordMatches = await bcrypt.compare(loginDto.password, user.password);
    if (!passwordMatches) {
      throw new BadRequestException('Invalid credentials');
    }

    delete user.password;

    // generate JWT token
    const tokens = await this.generateUserToken(user.id);

    return {
      message: 'Login successful',
      tokens
    }
  }

  async refreshToken(refreshTokenDto: refreshTokenDto) {
    const storedToken = await this.entityManager
      .createQueryBuilder(Auth_tokens, 'refresh_token')
      .where('refresh_token.token = :token', { token: refreshTokenDto.refreshToken })
      .andWhere('refresh_token.type = :type', { type: TokenType.REFRESH })
      .andWhere('refresh_token.expires_at > :now', { now: new Date() }) // Ensure token is not expired
      .getOne();

    if (!storedToken) {
      throw new UnauthorizedException('Invalid or expired refresh token');
    }
    await this.entityManager.createQueryBuilder()
      .update(Auth_tokens)
      .set({ expires_at: new Date() }) // Invalidate the used refresh token
      .where("id = :id", { id: storedToken.id })
      .execute();

    return this.generateUserToken(storedToken.user_id);
  }

  async generateUserToken(userId): Promise<any> {
    const accessToken = this.jwtService.sign({ id: userId }, { expiresIn: '24h' });
    const refreshToken = uuidv4();

    await this.storeRefreshToken(userId, refreshToken);

    return {
      accessToken,
      refreshToken
    };
  }

  async storeRefreshToken(userId: number, token: string) {
    // Calculate expiry 3 days from now 
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 3); // 3 days

    // Check if refresh token already exists for this user
    const existingToken = await this.entityManager
      .createQueryBuilder(Auth_tokens, 'auth_token')
      .where('auth_token.user_id = :userId', { userId })
      .andWhere('auth_token.type = :type', { type: TokenType.REFRESH })
      .getOne();

    if (existingToken) {
      // Update existing token
      await this.entityManager
        .createQueryBuilder()
        .update(Auth_tokens)
        .set({ token: token, expires_at: expiresAt })
        .where('id = :id', { id: existingToken.id })
        .execute();
    } else {
      // Insert new token
      await this.entityManager
        .createQueryBuilder()
        .insert()
        .into(Auth_tokens)
        .values({
          token: token,
          user_id: userId,
          expires_at: expiresAt,
          type: TokenType.REFRESH,
        })
        .execute();
    }
  }

  async changePassword(changePasswordDto: ChangePasswordDto, userId) {
    // find user by id and get password
    const user = await this.entityManager
      .createQueryBuilder(User, 'user')
      .where('user.id = :id', { id: userId })
      .getOne();

    if (!user) {
      throw new UnauthorizedException('User not found...... ');
    }
    // compare old password with one in db 
    const passwordMatches = await bcrypt.compare(changePasswordDto.oldPassword, user.password);
    if (!passwordMatches) {
      throw new BadRequestException('Old password is incorrect');
    }

    // change user password ( Dont forget to hash)
    const hashedNewPassword = await bcrypt.hash(changePasswordDto.newPassword, 12); // 12 salt rounds
    await this.entityManager.createQueryBuilder()
      .update(User)
      .set({ password: hashedNewPassword })
      .where('id = :id', { id: userId })
      .execute();

    return { message: 'Password changed successfully' };
  }

  async forgotPassword(forgotPasswordDto: forgotPasswordDto) {
    // check if user exist 
    const email = await this.normalizeEmail(forgotPasswordDto.email);
    const user = await this.findByEmail(email);

    if (user) {
      // if user exist generat password reset link
      const expiryDate = new Date();
      expiryDate.setHours(expiryDate.getHours() + 1); // 1 hour expiry

      const resetToken = nanoid(64);
      await this.entityManager
        .createQueryBuilder()
        .insert()
        .into(Auth_tokens)
        .values({
          token: resetToken,
          user_id: user.id,
          expires_at: expiryDate,
          type: TokenType.RESET,
        })
        .execute();
            
        // send email to user with link
        this.mailService.sendPasswordResetEmail(forgotPasswordDto.email, resetToken);
    }
    // weather user exist or not dont throw error dont let people know if email exist in system
    return { message: 'If that email is registered, you will receive a password reset link.' };
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto) {
    // find token in db
    const storedToken = await this.entityManager
      .createQueryBuilder(Auth_tokens, 'reset_token')
      .where('reset_token.token = :token', { token: resetPasswordDto.resetToken })
      .andWhere('reset_token.type = :type', { type: TokenType.RESET })
      .andWhere('reset_token.expires_at > :now', { now: new Date() }) // Ensure token is not expired
      .getOne();

    if (!storedToken) {
      throw new UnauthorizedException('Invalid or expired reset token');
    }

    // find user by id 
    const user = await this.entityManager
      .createQueryBuilder(User, 'user')
      .where('user.id = :id', { id: storedToken.user_id })
      .getOne();

    if (!user) {
      throw new UnauthorizedException('User not found');
    }
    
    // update user password 
    const hashedNewPassword = await bcrypt.hash(resetPasswordDto.newPassword, 12); // 12 salt rounds
    await this.entityManager.createQueryBuilder()
      .update(User)
      .set({ password: hashedNewPassword })
      .where('id = :id', { id: user.id })
      .execute();

    // invalidate the used reset token 
    await this.entityManager.createQueryBuilder()
      .update(Auth_tokens)
      .set({ expires_at: new Date() }) // Invalidate the used reset token
      .where("id = :id", { id: storedToken.id })
      .execute();

    return { message: 'Password has been reset successfully' };

  }
}
