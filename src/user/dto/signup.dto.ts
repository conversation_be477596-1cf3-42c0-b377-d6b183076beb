import { ApiProperty } from "@nestjs/swagger"
import { IsEmail, IsNotEmpty, IsString, Matches, Min, MinLength } from "class-validator"

export class signupDto{

  @ApiProperty({example: '<PERSON>'})
  @IsString()
  @IsNotEmpty()
  name : string 

  @ApiProperty({ example: '<EMAIL>'})
  @IsEmail()
  @IsNotEmpty()
  email : string 

  @ApiProperty({ example: 'strongPassword123!'})
  @IsString()
  @MinLength(6)
  @IsNotEmpty()
  @Matches(/^(?=.*[0-9])/ , { message: 'Paasword must contain at least one number'})
  password : string
}