import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsString, Matches, MinLength } from "class-validator";

export class ResetPasswordDto {
  @ApiProperty({ example: 'your-reset-token' })
  @IsNotEmpty()
  @IsString()
  resetToken: string;

  @ApiProperty({ example: 'kae123123' })
  @IsString()
  @MinLength(6)
  @IsNotEmpty()
  @Matches(/^(?=.*[0-9])/ , { message: 'Paasword must contain at least one number'})
  newPassword: string;
}