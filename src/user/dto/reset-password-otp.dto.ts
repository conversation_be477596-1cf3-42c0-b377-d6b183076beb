import { IsEmail, IsNumberString, Length, MinLength } from 'class-validator';

export class ResetPasswordOtpDto {
  @IsEmail()
  email: string;

  @IsNumberString({}, { message: 'OTP must contain only digits' })
  @Length(6, 6, { message: 'OTP must be exactly 6 digits' })
  otp: string;

  @MinLength(6, { message: 'Password must be at least 6 characters long' })
  newPassword: string;

  @MinLength(6, { message: 'Confirm password must be at least 6 characters long' })
  confirmPassword: string;
}
