import { Controller, Get, Post, Body, UseGuards, Req, Res, UnauthorizedException } from '@nestjs/common';
import { UserService } from './user.service';
import { signupDto } from './dto/signup.dto';
import { loginDto } from './dto/login.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { forgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ResetPasswordOtpDto } from './dto/reset-password-otp.dto';
import { VerifyOtpDto } from './dto/verify-otp.dto';
import { ResendOtpDto } from './dto/resend-otp.dto';
import { AuthGuard as JwtAuthGuard } from 'src/auth/auth.guard';
import type { Response } from 'express';

@Controller('user')
export class UserController {
  constructor(
    private readonly userService: UserService
  ) { }

  @Post('signup')
  async signup(@Body() signupDto: signupDto) {
    return await this.userService.signup(signupDto);
  }

  @Post('verify-otp')
  async verifyOtp(@Body() verifyOtpDto: VerifyOtpDto, @Res({ passthrough: true }) res: Response) {
    const result = await this.userService.verifyOtp(verifyOtpDto);

    // Set cookies after successful verification
    res.cookie('accessToken', result.tokens.accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000 // 24 hours
    });

    res.cookie('refreshToken', result.tokens.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 3 * 24 * 60 * 60 * 1000 // 3 days
    });

    return { message: result.message };
  }

  @Post('resend-otp')
  async resendOtp(@Body() resendOtpDto: ResendOtpDto) {
    return await this.userService.resendOtp(resendOtpDto);
  }

  @Post('login')
  async login(@Body() loginDto: loginDto, @Res({ passthrough: true }) res: Response) {
    const result = await this.userService.login(loginDto);

    res.cookie('accessToken', result.tokens.accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000 // 24 hours
    });

    res.cookie('refreshToken', result.tokens.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 3 * 24 * 60 * 60 * 1000 // 3 days
    });

    return { message: result.message };
  }

  @Post('refresh-token')
  async refreshToken(@Req() req: any, @Res({ passthrough: true }) res: Response) {
    // Add delay to prevent rapid-fire requests
    await new Promise(resolve => setTimeout(resolve, 500));

    const refreshToken = req.cookies?.refreshToken;

    if (!refreshToken) {
      res.clearCookie('accessToken');
      res.clearCookie('refreshToken');
      throw new UnauthorizedException('Refresh token not found in cookies. Please log in again.');
    }

    try {
      const tokens = await this.userService.refreshToken({ refreshToken });

      res.cookie('accessToken', tokens.accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60 * 1000
      });

      res.cookie('refreshToken', tokens.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 3 * 24 * 60 * 60 * 1000
      });

      return { message: 'Tokens refreshed successfully' };
    } catch (error) {
      // Clear cookies on refresh failure
      res.clearCookie('accessToken');
      res.clearCookie('refreshToken');
      throw new UnauthorizedException('Invalid refresh token. Please log in again.');
    }
  }

  @UseGuards(JwtAuthGuard)
  @Get('me')
  async getMe(@Req() req: any) {
    const user = await this.userService.findById(req.userId);
    if (!user) {
      throw new UnauthorizedException('User not found');
    }
    return { user };
  }

  @UseGuards(JwtAuthGuard)
  @Post('change-password')
  async changePassword(@Body() changePasswordDto: ChangePasswordDto, @Req() req: any) {
    return await this.userService.changePassword(changePasswordDto, req.userId);
  }

  @Post('forgot-password')
  async forgotPassword(@Body() forgotPasswordDto: forgotPasswordDto) {
    return await this.userService.forgotPassword(forgotPasswordDto);
  }

  @Post('forgot-password-otp')
  async forgotPasswordOtp(@Body() forgotPasswordDto: forgotPasswordDto) {
    return await this.userService.forgotPasswordOtp(forgotPasswordDto);
  }

  @Post('reset-password')
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return await this.userService.resetPassword(resetPasswordDto);
  }

  @Post('reset-password-otp')
  async resetPasswordOtp(@Body() resetPasswordOtpDto: ResetPasswordOtpDto) {
    return await this.userService.resetPasswordWithOtp(resetPasswordOtpDto);
  }

  @Post('logout')
  async logout(@Res({ passthrough: true }) res: Response) {
    res.clearCookie('accessToken');
    res.clearCookie('refreshToken');
    return { message: 'Logged out successfully' };
  }

  // Admin endpoint for cleanup (remove in production or add admin guard)
  @Post('cleanup-unverified')
  async cleanupUnverified() {
    const count = await this.userService.cleanupUnverifiedAccounts();
    return { message: `Cleaned up ${count} unverified accounts` };
  }
}
