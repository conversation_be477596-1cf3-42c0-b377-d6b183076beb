import { Injectable } from "@nestjs/common";
import { Strategy } from "passport-google-oauth20";
import { PassportStrategy } from '@nestjs/passport';
import configuration from "config/configuration";

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  constructor() {
    super({
      clientID: configuration().google.clientID,   // from .env
      clientSecret: configuration().google.clientSecret, // from .env
      callbackURL: configuration().google.callbackURL, // e.g. http://localhost:3000/auth/google/callback
      scope: ['email', 'profile'],
    });
  }

  async validate(accessToken: string, refreshToken: string, profile: any, done: Function) {
    const { id, name, emails, photos } = profile;
    const user = {
      id,
      email: emails[0].value,
      firstName: name.givenName,
      lastName: name.familyName,
      picture: photos[0].value,
      accessToken
    }
    done(null, user);
  }

}