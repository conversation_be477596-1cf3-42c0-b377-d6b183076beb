import { Controller, Get, UseGuards, Req, Res } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AuthGuard as JwtAuthGuard } from './auth.guard';
import { OauthService } from 'src/services/oauth.service';
import { UserService } from 'src/user/user.service';
import { ConfigService } from '@nestjs/config';
import type { Response } from 'express';

@Controller('auth')
export class AuthController {
  constructor(
    private readonly oauthService: OauthService,
    private readonly userService: UserService,
    private readonly configService: ConfigService,
  ) { }

  @Get('google')
  @UseGuards(AuthGuard('google'))
  async googleAuth() {
    // this redirects to Google
  }

  @Get('google/callback')
  @UseGuards(AuthGuard('google'))
  async googleAuthRedirect(@Req() req, @Res() res: Response) {
    try {
      const tokens = await this.oauthService.handleGoogleLogin(req.user);

      res.cookie('accessToken', tokens.accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
      });

      res.cookie('refreshToken', tokens.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 3 * 24 * 60 * 60 * 1000 // 3 days
      });

      // Redirect to frontend success page
      const frontendUrl = this.configService.get('FRONTEND_URL') || 'http://localhost:5173';
      res.redirect(`${frontendUrl}/auth/success`);
    } catch (error) {
      // Redirect to frontend error page
      const frontendUrl = this.configService.get('FRONTEND_URL') || 'http://localhost:5173';
      res.redirect(`${frontendUrl}/auth/error?message=${encodeURIComponent(error.message)}`);
    }
  }

  @UseGuards(JwtAuthGuard)
  @Get('status')
  async getAuthStatus(@Req() req: any) {
    const user = await this.userService.findById(req.userId);
    return {
      isAuthenticated: true,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        isOAuthUser: !!user.google_id,
        hasPassword: !!user.password,
        isVerified: user.is_verified
      }
    };
  }
}
