// auth.controller.ts
import { <PERSON>, Get, UseGuards, Req, Res } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { OauthService } from 'src/services/oauth.service';
import { Response } from 'express';

@Controller('auth')
export class AuthController {
  constructor(private readonly oauthService: OauthService) { }
  @Get('google')
  @UseGuards(AuthGuard('google'))
  async googleAuth() {
    // this redirects to Google
  }

  @Get('google/callback')
  @UseGuards(AuthGuard('google'))
  async googleAuthRedirect(@Req() req, @Res({ passthrough: true }) res: Response) {
    const tokens = await this.oauthService.handleGoogleLogin(req.user);
    
    res.cookie('accessToken', tokens.accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000 // 24 hours
    });
    
    res.cookie('refreshToken', tokens.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 3 * 24 * 60 * 60 * 1000 // 3 days
    });

    return {
      message: 'Google login successful'
    };
  }
}
