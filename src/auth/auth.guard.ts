import { CanActivate, ExecutionContext, Injectable, Logger, UnauthorizedException } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { Observable } from "rxjs";

interface AuthenticatedRequest extends Request {
  userId: number;
  cookies: any;
}

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private jwtService: JwtService,
  ){}

  canActivate(    
    context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
      const request = context.switchToHttp().getRequest() as AuthenticatedRequest;
      const token = this.extractTokenFromCookie(request);

      if (!token) {
        throw new UnauthorizedException('Invalid token');
      }
      try {
        const payload = this.jwtService.verify(token);
        request.userId = payload.id;  // Attach user ID to request object
      }catch(error){
        Logger.error(error);
        throw new UnauthorizedException('Invalid token');
      }
      return true
    }

    private extractTokenFromCookie(request: AuthenticatedRequest): string | null {
      return request.cookies?.accessToken || null;
    }

}
