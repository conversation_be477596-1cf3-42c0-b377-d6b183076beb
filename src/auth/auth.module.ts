import { Module, forwardRef } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { AuthController } from './auth.controller';
import { GoogleStrategy } from './google.strategy';
import { UserModule } from 'src/user/user.module';

@Module({
  imports: [
    PassportModule.register({ session: false }), 
    forwardRef(() => UserModule)
  ],
  controllers: [AuthController],
  providers: [GoogleStrategy],
  exports: [GoogleStrategy, PassportModule],
})
export class AuthModule {}
