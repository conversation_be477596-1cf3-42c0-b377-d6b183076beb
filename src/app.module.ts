import { Modu<PERSON> } from '@nestjs/common';
import { App<PERSON>ontroller } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserModule } from './user/user.module';
import { User } from './user/entities/user.entity';
import configuration from 'config/configuration';
import { JwtModule } from '@nestjs/jwt';
import { Auth_tokens } from './user/entities/auth_tokens.entity';
import { AuthModule } from './auth/auth.module';

@Module({
  imports: [
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (config) => ({
        secret: config.get('jwt.secret')
      }),
      global: true,
      inject: [ConfigService],
    }),
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      load: [configuration], // Maps raw .env vars into a structured config object
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: configuration().database.host,
      port: configuration().database.port,
      username: configuration().database.username,
      password: configuration().database.password,
      database: configuration().database.database,
      schema: configuration().database.schema,

      entities: [
        User,
        Auth_tokens,
        
      ],

      synchronize: configuration().database.synchronize === 'true' ? true : false,
      dropSchema: false,   // Don't drop schema on startup
      //  logging: true,   // Enable logging in dev
    }),
    UserModule,
    AuthModule,

  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule { }
