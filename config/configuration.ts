require('dotenv').config();

export default () => ({
  port: parseInt(process.env.PORT, 10) || 3000,
  database: {
    type: 'postgres',
    host: process.env.DATABASE_HOST,
    port: parseInt(process.env.DATABASE_PORT),
    username: process.env.DATABASE_USERNAME,
    password: process.env.DATABASE_PASSWORD,
    database: process.env.DATABASE_NAME,
    schema: process.env.DATABASE_SCHEMA,    
    entities: ['dist/**/*.entity{.ts,.js}'],   // tells TypeORM where to find your entities
    migrations: ['src/migration/**/*.ts'],     // tells TypeORM where to find your migrations
    synchronize: process.env.SYNC,
    charset: 'utf8mb4',                           // for supporting emojis
    seeder_sync: process.env.SEEDER_SYNC,
  },
  encryption: {
    secret: process.env.ENCRYPTION_SECRET,
  },
  jwt: {
    secret: process.env.JWT_SECRET,
  },
  redis: {
    host: process.env.REDIS_HOST,
    port: parseInt(process.env.REDIS_PORT, 10) || 6379,
    password: process.env.REDIS_PASSWORD,
  },
  google: {
    clientID: process.env.GOOGLE_CLIENT_ID,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    callbackURL: process.env.GOOGLE_CALLBACK_URL,
  },
});
