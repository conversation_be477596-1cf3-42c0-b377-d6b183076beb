-- Add OTP verification fields to users table
ALTER TABLE users 
ADD COLUMN is_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN otp_code VARCHAR(6),
ADD COLUMN otp_expires_at TIMESTAMP,
ADD COLUMN otp_attempts INTEGER DEFAULT 0,
ADD COLUMN last_otp_request TIMESTAMP;

-- Update existing users to be verified (for Google OAuth users)
UPDATE users SET is_verified = TRUE WHERE google_id IS NOT NULL;

-- Create index for better performance
CREATE INDEX idx_users_otp_code ON users(otp_code);
CREATE INDEX idx_users_is_verified ON users(is_verified);
